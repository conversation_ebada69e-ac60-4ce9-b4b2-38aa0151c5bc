# DSB数据采集与存储系统工程设计文档

**队伍编号：** 2025815992  
**学校：** 安徽工业大学  
**开发环境：** Keil MDK 5.06 (build 960)  
**开发平台：** GD32F470VET6微控制器  
**开发库：** GD32标准库函数  

---

## 第一章 工程任务分析

### 1.1 项目背景与意义

本项目是基于GD32F470VET6微控制器的数据采集与存储系统（DSB），旨在实现高精度、高可靠性的模拟信号采集、处理、存储和管理功能。系统具备实时数据采集、多种存储模式、时间管理、用户交互等核心功能，适用于工业监测、科研实验、数据记录等多种应用场景。

### 1.2 系统功能需求分析

#### 1.2.1 核心功能需求

**1. 数据采集功能**
- 基于ADC的模拟信号采集，支持电压信号测量
- 可配置采样周期：5秒、10秒、15秒三档可选
- 支持变比配置（0-100范围），实现信号调理
- 实时电压值计算和显示，精度达到小数点后两位

**2. 数据存储功能**
- 基于SD卡的海量数据存储，支持FAT文件系统
- 多文件夹分类存储：sample（正常采样）、overLimit（超限数据）、hideData（加密数据）、log（系统日志）
- 每个文件最多存储10条数据，自动创建新文件
- 支持数据加密存储模式，提供数据安全保护

**3. 时间管理功能**
- 基于RTC的实时时钟管理，支持年月日时分秒完整时间信息
- 支持多种时间格式输入和显示
- 时间戳功能，为每条数据记录精确的时间信息
- 支持Unix时间戳转换，便于数据处理和分析

**4. 用户交互功能**
- 多按键操作界面，支持采样控制和参数设置
- OLED显示屏实时显示系统状态和数据信息
- 串口通信接口，支持命令行操作和参数配置
- LED指示灯状态显示，直观反映系统工作状态

#### 1.2.2 扩展功能需求

**1. 系统监控功能**
- 开机次数统计和管理
- 系统自检功能，检测Flash、SD卡、RTC等关键组件
- 数据计数统计，实时监控数据存储状态
- 系统日志记录，追踪系统运行状态和操作历史

**2. 配置管理功能**
- 参数持久化存储，支持Flash和SD卡配置文件
- 阈值管理，支持超限检测和报警
- 加密模式切换，保护敏感数据
- 配置文件读取，支持批量参数设置

### 1.3 技术指标要求

#### 1.3.1 硬件性能指标

**1. 处理器性能**
- 主控芯片：GD32F470VET6，ARM Cortex-M4内核
- 主频：200MHz，提供强大的数据处理能力
- Flash存储：512KB，用于程序存储和参数保存
- RAM容量：192KB，满足数据缓存和处理需求

**2. 采集精度指标**
- ADC分辨率：12位，提供4096级精度
- 采样精度：±0.01V，满足高精度测量需求
- 采样频率：可配置5s/10s/15s周期采样
- 变比范围：0-100倍，支持信号调理

**3. 存储容量指标**
- 内部Flash：512KB，用于系统参数和配置存储
- 外部SD卡：支持GB级大容量存储
- 数据格式：文本格式，便于数据分析和处理
- 文件管理：自动分类存储，支持多文件夹结构

#### 1.3.2 软件性能指标

**1. 实时性指标**
- 任务调度周期：1ms-50ms，保证系统实时响应
- 数据采集延迟：<100ms，满足实时性要求
- 按键响应时间：<50ms，提供良好用户体验
- 显示刷新频率：10Hz，保证显示流畅性

**2. 可靠性指标**
- 系统稳定运行时间：>24小时连续工作
- 数据存储可靠性：>99.9%，确保数据完整性
- 错误恢复能力：支持异常检测和自动恢复
- 电源管理：支持低功耗模式和电源监控

### 1.4 工程目标与约束条件

#### 1.4.1 工程目标

**1. 主要目标**
- 实现稳定可靠的数据采集与存储系统
- 提供友好的用户操作界面和丰富的功能选项
- 确保数据的完整性、准确性和安全性
- 支持长期连续运行和大容量数据存储

**2. 性能目标**
- 采集精度达到±0.01V
- 系统响应时间<100ms
- 连续工作时间>24小时
- 数据存储容量>1GB

#### 1.4.2 约束条件

**1. 硬件约束**
- 基于GD32F470VET6微控制器平台
- 使用GD32标准库函数开发
- 受限于芯片的Flash和RAM容量
- 依赖外部SD卡进行大容量存储

**2. 软件约束**
- 使用Keil MDK 5.06开发环境
- 需要CMSIS.5.4.0.pack支持包
- 需要GorgonMeducer.perf_counter.2.4.0.pack性能计数器支持
- 采用实时操作系统调度机制

**3. 功能约束**
- 单通道ADC采集，扩展性有限
- SD卡依赖性，需要外部存储设备
- 显示屏尺寸限制，信息显示有限
- 串口通信速率限制

### 1.5 系统应用场景

#### 1.5.1 工业监测应用
- 设备运行状态监测
- 环境参数长期记录
- 生产过程数据采集
- 质量控制数据分析

#### 1.5.2 科研实验应用
- 实验数据自动采集
- 长期观测数据记录
- 多参数同步监测
- 实验结果数据分析

#### 1.5.3 教学演示应用
- 嵌入式系统教学
- 数据采集原理演示
- 实时系统设计教学
- 工程项目实践训练

### 1.6 技术创新点

#### 1.6.1 系统架构创新
- 模块化设计架构，各功能模块独立可配置
- 多层次数据存储策略，提高数据安全性
- 灵活的任务调度机制，优化系统资源利用
- 完善的错误处理和恢复机制

#### 1.6.2 功能实现创新
- 加密数据存储模式，保护敏感信息
- 智能文件管理系统，自动分类和归档
- 多格式时间输入支持，提高用户体验
- 丰富的系统监控和诊断功能

#### 1.6.3 用户体验创新
- 直观的LED状态指示系统
- 友好的OLED显示界面
- 便捷的串口命令行操作
- 灵活的参数配置方式

---

## 第二章 系统单元功能分析设计

### 2.1 系统总体架构

DSB数据采集与存储系统采用模块化设计架构，主要包括以下核心功能模块：

- **ADC采样模块**：负责模拟信号采集和数字化转换
- **数据存储模块**：负责数据的分类存储和文件管理
- **时间管理模块**：负责实时时钟和时间戳管理
- **用户交互模块**：负责按键输入、显示输出和串口通信
- **系统调度模块**：负责任务调度和系统资源管理
- **配置管理模块**：负责参数存储和配置文件管理

#### 2.1.1 系统架构设计原则

**1. 模块化设计原则**
- 各功能模块相对独立，接口清晰
- 模块间通过标准接口进行数据交换
- 支持模块的独立测试和维护
- 便于功能扩展和系统升级

**2. 分层设计原则**
- 硬件抽象层（HAL）：屏蔽硬件差异
- 驱动层（Driver）：提供设备驱动接口
- 应用层（Application）：实现业务逻辑功能
- 用户接口层（UI）：提供人机交互界面

**3. 实时性设计原则**
- 采用基于优先级的任务调度机制
- 关键任务具有更高的执行优先级
- 保证系统的实时响应能力
- 优化中断处理和任务切换开销

### 2.2 ADC采样模块设计

#### 2.2.1 ADC采样原理

ADC采样模块是系统的核心功能模块，负责将模拟电压信号转换为数字量，并进行相应的数据处理。

**1. 硬件配置**
- 使用GD32F470VET6内置的12位ADC
- 采样分辨率：4096级（0-4095）
- 参考电压：3.3V
- 采样通道：单通道采集模式

**2. 采样流程设计**

```
模拟信号输入 → 信号调理 → ADC转换 → 数字滤波 → 数据处理 → 结果输出
```

**采样控制流程：**
1. 系统初始化时配置ADC参数
2. 定时器触发ADC采样启动
3. ADC完成转换后产生中断
4. 中断服务程序读取ADC值
5. 对原始数据进行处理和转换
6. 将处理结果存储到缓冲区

#### 2.2.2 采样周期控制

系统支持三种采样周期模式，通过按键或串口命令进行切换：

**1. 采样周期配置**
- 5秒周期：KeyNum = 2，适用于快速监测
- 10秒周期：KeyNum = 3，适用于常规监测
- 15秒周期：KeyNum = 4，适用于长期监测

**2. 定时器配置策略**
- 使用TIMER6作为采样定时器
- 基础定时周期为5秒
- 通过计数器实现不同周期的采样控制
- 支持动态周期切换，无需重启系统

**3. 采样控制逻辑**

```c
// 采样周期控制逻辑示例
void TIMER6_IRQHandler(void)
{
    static uint8_t cycle_counter = 0;

    if (timer_interrupt_flag_get(TIMER6, TIMER_INT_FLAG_UP) == SET) {
        timer_interrupt_flag_clear(TIMER6, TIMER_INT_FLAG_UP);
        cycle_counter++;

        uint8_t should_execute = 0;

        if (KeyNum == 3) {
            // 10秒周期：每两个周期执行一次
            if (cycle_counter >= 2) {
                should_execute = 1;
                cycle_counter = 0;
            }
        } else if (KeyNum == 4) {
            // 15秒周期：每三个周期执行一次
            if (cycle_counter >= 3) {
                should_execute = 1;
                cycle_counter = 0;
            }
        } else {
            // 5秒周期：每个周期都执行
            should_execute = 1;
            cycle_counter = 0;
        }

        if (should_execute) {
            timer6_execute_flag = 1;  // 设置采样标志
        }
    }
}
```

#### 2.2.3 数据处理算法

**1. 电压值转换算法**

原始ADC值需要转换为实际电压值，转换公式为：
```
实际电压 = (ADC值 × 参考电压 × 变比) / ADC最大值
实际电压 = (adc_val × 3.3 × input_radio) / 4095
```

**2. 变比配置功能**
- 变比范围：0.01 - 100.00
- 默认变比：1.00
- 支持动态配置和Flash存储
- 用于信号调理和量程扩展

**3. 数据精度控制**
- 电压显示精度：小数点后2位
- 内部计算精度：浮点数运算
- 数据有效性检查：防止异常值
- 滤波处理：减少噪声干扰

#### 2.2.4 超限检测机制

**1. 阈值管理**
- 阈值范围：0.01 - 200.00V
- 默认阈值：1.00V
- 支持动态配置和持久化存储
- 实时阈值比较和报警

**2. 超限处理流程**
```
ADC采样 → 电压转换 → 阈值比较 → 超限判断 → 报警处理 → 数据存储
```

**3. 报警指示方式**
- LED2指示灯点亮：视觉报警
- 串口输出"OverLimit"信息：文字报警
- 超限数据单独存储：便于后续分析
- 加密模式下添加"*"标记：特殊标识

### 2.3 数据存储模块设计

#### 2.3.1 存储架构设计

数据存储模块采用分层存储架构，包括内部Flash存储和外部SD卡存储两个层次。

**1. 内部Flash存储**
- 存储内容：系统配置参数、设备ID、开机计数等
- 存储特点：断电保持、读写速度快、容量有限
- 管理方式：扇区擦除、页编程、数据校验

**2. 外部SD卡存储**
- 存储内容：采样数据、超限数据、加密数据、系统日志
- 存储特点：大容量、可移动、FAT文件系统
- 管理方式：文件夹分类、自动命名、定期归档

#### 2.3.2 文件系统设计

**1. 目录结构设计**
```
SD卡根目录/
├── sample/          # 正常采样数据
│   ├── sampleData20250101120000.txt
│   └── sampleData20250101130000.txt
├── overLimit/       # 超限数据
│   ├── overLimit20250101120000.txt
│   └── overLimit20250101130000.txt
├── hideData/        # 加密数据
│   ├── hideData20250101120000.txt
│   └── hideData20250101130000.txt
├── log/            # 系统日志
│   ├── log0.txt
│   └── log1.txt
└── config.ini      # 配置文件
```

**2. 文件命名规则**
- 时间戳命名：使用YYYYMMDDHHmmss格式
- 自动递增：同类型文件按时间顺序命名
- 扩展名统一：使用.txt文本格式
- 便于识别：文件名包含数据类型信息

#### 2.3.3 数据存储策略

**1. 分类存储策略**

根据数据类型和用途，系统将数据分为四类进行存储：

- **正常采样数据（sample）**：存储在正常模式下的所有采样数据
- **超限数据（overLimit）**：单独存储超过阈值的数据，便于分析
- **加密数据（hideData）**：加密模式下的数据，提供安全保护
- **系统日志（log）**：记录系统操作和状态变化

**2. 文件管理策略**

- **容量控制**：每个数据文件最多存储10条记录
- **自动切换**：达到容量限制时自动创建新文件
- **时间戳管理**：使用统一的时间戳生成机制
- **状态跟踪**：维护文件打开状态和计数器

**3. 数据格式设计**

**正常采样数据格式：**
```
2025-01-01 12:00:00 12.34V
2025-01-01 12:00:05 12.35V
```

**超限数据格式：**
```
2025-01-01 12:00:00 35.67V limit 30.00V
2025-01-01 12:00:05 36.78V limit 30.00V
```

**加密数据格式：**
```
2025-01-01 12:00:00 12.34V
hide: 1A2B3C4D5E6F7890
2025-01-01 12:00:05 35.67V
hide: 1A2B3C4D5E6F7890*
```

**系统日志格式：**
```
System started at 2025-01-01 12:00:00 (Power on count: 1)
[2025-01-01 12:00:01] system init
[2025-01-01 12:00:02] system hardware test
[2025-01-01 12:00:03] test ok
```

#### 2.3.4 加密存储机制

**1. 加密算法设计**

系统采用自定义的数据编码算法，将电压值转换为十六进制格式：

- **时间戳编码**：Unix时间戳转换为8位十六进制
- **电压值编码**：浮点电压值转换为8位十六进制
- **超限标记**：超限数据末尾添加"*"标识
- **数据完整性**：原始数据和加密数据同时存储

**2. 加密数据处理流程**
```
电压采样 → 时间戳获取 → 数据编码 → 超限检查 → 加密存储 → 串口输出
```

**3. 加密模式控制**
- 通过"hide"命令启用加密模式
- 通过"unhide"命令禁用加密模式
- 加密状态持久化保存
- 模式切换时重置时间戳

### 2.4 时间管理模块设计

#### 2.4.1 RTC时钟系统

**1. RTC硬件配置**
- 时钟源：外部32.768kHz晶振（LXTAL）
- 时钟精度：±20ppm（约±1.7秒/天）
- 时间格式：24小时制，BCD编码
- 备份电源：支持主电源断电时保持时间

**2. 时间数据结构**
```c
typedef struct {
    uint8_t year;        // 年份（BCD格式，00-99）
    uint8_t month;       // 月份（BCD格式，01-12）
    uint8_t date;        // 日期（BCD格式，01-31）
    uint8_t hour;        // 小时（BCD格式，00-23）
    uint8_t minute;      // 分钟（BCD格式，00-59）
    uint8_t second;      // 秒钟（BCD格式，00-59）
    uint8_t day_of_week; // 星期（1-7，周一到周日）
} rtc_parameter_struct;
```

#### 2.4.2 时间设置与显示

**1. 时间输入格式支持**
- 中文格式：2025年01月01日12:00:30
- 标准格式：2025-01-01 12:00:30
- 紧凑格式：2025-01-01 01-30-10

**2. 时间解析算法**
```c
// 时间字符串解析示例
static RTC_StatusTypeDef parse_time_string_gd32(const char *time_str, rtc_parameter_struct *rtc_param)
{
    int year, month, day, hour, minute, second;
    int parsed = 0;

    // 尝试解析中文格式
    parsed = sscanf(time_str, "%d年%d月%d日%d:%d:%d", &year, &month, &day, &hour, &minute, &second);

    // 如果失败，尝试标准格式
    if (parsed != 6) {
        parsed = sscanf(time_str, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second);
    }

    // 参数验证和BCD转换
    if (parsed == 6 && validate_time_params(year, month, day, hour, minute, second)) {
        rtc_param->year = decimal_to_bcd(year - 2000);
        rtc_param->month = decimal_to_bcd(month);
        rtc_param->date = decimal_to_bcd(day);
        rtc_param->hour = decimal_to_bcd(hour);
        rtc_param->minute = decimal_to_bcd(minute);
        rtc_param->second = decimal_to_bcd(second);
        return RTC_STATUS_OK;
    }

    return RTC_STATUS_ERROR;
}
```

#### 2.4.3 时间戳管理

**1. Unix时间戳转换**
- 将RTC时间转换为Unix时间戳
- 用于加密数据的时间标识
- 便于跨平台数据处理和分析

**2. 时间戳生成流程**
```
RTC读取 → BCD转十进制 → 日期时间组合 → Unix时间戳计算 → 十六进制转换
```

**3. 时间同步机制**
- 系统启动时检查RTC状态
- 支持手动时间校准
- 定期时间同步检查
- 时间设置操作日志记录

### 2.5 用户交互模块设计

#### 2.5.1 按键输入系统

**1. 按键功能定义**
- KEY3：采样启动/停止控制
- KEY4：切换到5秒采样周期
- KEY5：切换到15秒采样周期
- KEY6：切换到10秒采样周期

**2. 按键处理机制**
```c
void btn_task(void)
{
    // KEY3：采样控制
    if (KEY3_READ == 0) {
        delay_ms(20);                    // 消抖延时
        while (KEY3_READ == 0);          // 等待按键释放
        delay_ms(20);                    // 消抖延时
        pre_KeyNum = 1;                  // 设置采样切换标志
        store_log_entry("sample stop (key press)");
        reset_data_storage_system();
    }

    // KEY4：5秒周期
    if (KEY4_READ == 0) {
        delay_ms(20);
        while (KEY4_READ == 0);
        delay_ms(20);
        KeyNum = 2;
        my_printf(DEBUG_USART, "sample cycle adjust: 5s\r\n");
        store_log_entry("cycle switch to 5s (key press)");
        flash_keynum_stored_silent(KeyNum);
    }

    // 其他按键处理...
}
```

**3. 按键防抖处理**
- 硬件防抖：RC滤波电路
- 软件防抖：20ms延时确认
- 状态检测：等待按键释放
- 重复触发防护：状态标志控制

#### 2.5.2 OLED显示系统

**1. 显示内容设计**
- 第一行：系统状态或实时时间
- 第二行：当前电压值或系统信息
- 状态切换：根据系统工作模式动态显示

**2. 显示控制逻辑**
```c
void rtc_task(void)
{
    rtc_current_time_get(&rtc_initpara);

    if (oled_flag) {
        // 采样模式：显示时间
        oled_printf(0, 0, "%0.2x:%0.2x:%0.2x",
                   rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
    } else {
        // 空闲模式：显示状态
        oled_printf(0, 0, "system idle");
    }
}

void adc_process_task(void)
{
    if (timer6_execute_flag == 1) {
        // 显示电压值
        oled_printf(0, 1, "Voltage: %.2fV", Vol_Value);
    }
}
```

#### 2.5.3 串口通信系统

**1. 串口命令系统**

系统支持丰富的串口命令，实现远程控制和参数配置：

**基本控制命令：**
- START：启动采样
- STOP：停止采样
- test：系统自检

**配置命令：**
- radio：设置变比参数
- limit：设置阈值参数
- RTC Config：设置系统时间
- RTC now：显示当前时间

**数据管理命令：**
- hide：启用加密模式
- unhide：禁用加密模式
- power count：显示开机次数
- data count：显示数据计数

**2. 命令解析机制**
```c
void parse_uart_command(uint8_t *buffer, uint16_t length)
{
    if (strcmp((char *)temp_buffer, "START") == 0) {
        // 启动采样处理
        KeyNum = flash_keynum_read_silent();
        uint8_t cycle = (KeyNum - 1) * 5;
        timer_enable(TIMER4);
        timer_enable(TIMER6);
        OLED_Clear();
        my_printf(DEBUG_USART, "Periodic Sampling\r\n");
        my_printf(DEBUG_USART, "sample cycle: %d\r\n", cycle);
        oled_flag = 1;
        store_log_entry("sample start - cycle 5s (command)");
    }
    else if (strcmp((char *)temp_buffer, "STOP") == 0) {
        // 停止采样处理
        my_printf(DEBUG_USART, "Periodic Sampling STOP\r\n");
        OLED_Clear();
        oled_flag = 0;
        timer_disable(TIMER4);
        timer_disable(TIMER6);
        LED1_OFF;
        store_log_entry("sample stop (command)");
        reset_data_storage_system();
    }
    // 其他命令处理...
}
```

#### 2.5.4 LED指示系统

**1. LED功能定义**
- LED1：采样状态指示，采样时闪烁
- LED2：超限报警指示，超限时点亮
- LED3-LED6：按键状态指示，按键按下时切换

**2. LED控制策略**
- 状态指示：通过LED状态反映系统工作状态
- 闪烁控制：使用定时器控制LED闪烁频率
- 优先级管理：报警指示优先于状态指示
- 节能考虑：空闲时关闭非必要LED

### 2.6 系统调度模块设计

#### 2.6.1 任务调度架构

**1. 调度器设计原则**
- 基于时间片的协作式调度
- 支持不同优先级的任务执行
- 任务执行周期可配置
- 系统资源合理分配

**2. 任务定义结构**
```c
typedef struct {
    void (*task_func)(void);    // 任务函数指针
    uint32_t rate_ms;          // 执行周期（毫秒）
    uint32_t last_run;         // 上次运行时间
} task_t;
```

#### 2.6.2 任务配置与管理

**1. 系统任务列表**
```c
static task_t scheduler_task[] = {
    {adc_flip_task,     5,  0},   // ADC控制任务，5ms周期
    {btn_task,          5,  0},   // 按键处理任务，5ms周期
    {uart_task,         5,  0},   // 串口处理任务，5ms周期
    {rtc_task,         50,  0},   // RTC显示任务，50ms周期
    {adc_process_task,  1,  0},   // ADC处理任务，1ms周期
};
```

**2. 调度执行逻辑**
```c
void scheduler_run(void)
{
    for (uint8_t i = 0; i < task_num; i++) {
        uint32_t now_time = get_system_ms();

        // 检查任务是否到达执行时间
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run) {
            scheduler_task[i].last_run = now_time;  // 更新执行时间
            scheduler_task[i].task_func();          // 执行任务函数
        }
    }
}
```

---

## 第三章 综合系统设计

### 3.1 系统整体架构设计

#### 3.1.1 系统架构概述

DSB数据采集与存储系统采用分层模块化架构设计，整个系统可以分为四个主要层次：

**1. 硬件抽象层（Hardware Abstraction Layer, HAL）**
- 提供统一的硬件接口抽象
- 屏蔽不同硬件平台的差异
- 包括GPIO、ADC、TIMER、USART、SPI等外设驱动

**2. 系统服务层（System Service Layer）**
- 提供系统级服务功能
- 包括任务调度、时间管理、内存管理等
- 为应用层提供基础服务支持

**3. 应用功能层（Application Function Layer）**
- 实现具体的业务逻辑功能
- 包括数据采集、存储管理、用户交互等
- 各功能模块相对独立，通过接口进行交互

**4. 用户接口层（User Interface Layer）**
- 提供人机交互界面
- 包括按键输入、显示输出、串口通信等
- 为用户提供友好的操作体验

#### 3.1.2 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    用户接口层 (UI Layer)                      │
├─────────────┬─────────────┬─────────────┬─────────────────────┤
│   按键输入   │   OLED显示   │   LED指示   │     串口通信        │
└─────────────┴─────────────┴─────────────┴─────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  应用功能层 (Application Layer)               │
├─────────────┬─────────────┬─────────────┬─────────────────────┤
│   ADC采样   │   数据存储   │   时间管理   │     配置管理        │
│    模块     │     模块     │     模块     │       模块          │
└─────────────┴─────────────┴─────────────┴─────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                 系统服务层 (Service Layer)                   │
├─────────────┬─────────────┬─────────────┬─────────────────────┤
│   任务调度   │   中断管理   │   错误处理   │     资源管理        │
└─────────────┴─────────────┴─────────────┴─────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                 硬件抽象层 (HAL Layer)                       │
├─────────────┬─────────────┬─────────────┬─────────────────────┤
│    GPIO     │     ADC     │    TIMER    │   USART/SPI/I2C     │
└─────────────┴─────────────┴─────────────┴─────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    硬件平台 (Hardware)                       │
│              GD32F470VET6 + 外围器件                        │
└─────────────────────────────────────────────────────────────┘
```

#### 3.1.3 模块间关系设计

**1. 核心控制模块**
- 任务调度器作为系统核心，协调各模块运行
- 采用事件驱动和时间触发相结合的调度策略
- 保证系统实时性和资源利用效率

**2. 数据流向设计**
```
ADC采样 → 数据处理 → 存储分发 → 用户显示
    ↓         ↓         ↓         ↓
  中断触发   算法处理   文件管理   界面更新
```

**3. 控制流向设计**
```
用户输入 → 命令解析 → 功能调用 → 状态反馈
    ↓         ↓         ↓         ↓
  按键/串口  参数配置   模块控制   LED/显示
```

### 3.2 模块间接口设计

#### 3.2.1 接口设计原则

**1. 标准化接口**
- 定义统一的接口规范和数据格式
- 使用标准的函数调用和参数传递方式
- 保证接口的兼容性和可扩展性

**2. 松耦合设计**
- 模块间通过接口进行交互，减少直接依赖
- 支持模块的独立开发和测试
- 便于系统维护和功能扩展

**3. 数据封装**
- 模块内部数据结构对外透明
- 通过接口函数访问和操作数据
- 保证数据的完整性和安全性

#### 3.2.2 主要接口定义

**1. ADC采样接口**
```c
// ADC初始化接口
void adc_init(void);

// ADC采样控制接口
void adc_start_sampling(uint8_t cycle_mode);
void adc_stop_sampling(void);

// ADC数据获取接口
float adc_get_voltage(void);
uint32_t adc_get_raw_value(void);

// ADC状态查询接口
uint8_t adc_is_sampling(void);
uint8_t adc_is_overlimit(void);
```

**2. 数据存储接口**
```c
// 存储初始化接口
uint8_t storage_init(void);

// 数据存储接口
void store_sample_data(float voltage);
void store_overlimit_data(float voltage);
void store_hide_data(uint32_t timestamp, float voltage);
void store_log_entry(const char *action);

// 存储状态接口
uint8_t storage_is_ready(void);
uint16_t storage_get_data_count(void);
void storage_reset_system(void);
```

**3. 时间管理接口**
```c
// RTC初始化接口
void rtc_init(void);

// 时间设置接口
RTC_StatusTypeDef rtc_set_time_from_string(const char *time_str);

// 时间获取接口
void rtc_get_current_time(rtc_parameter_struct *time);
uint32_t rtc_get_unix_timestamp(void);

// 时间显示接口
void rtc_show_time(void);
void rtc_show_adc_time(void);
```

**4. 用户交互接口**
```c
// 按键处理接口
void btn_init(void);
void btn_process(void);
uint8_t btn_get_key_state(uint8_t key_id);

// 显示控制接口
void display_init(void);
void display_voltage(float voltage);
void display_time(rtc_parameter_struct *time);
void display_status(const char *status);

// 串口通信接口
void uart_init(void);
void uart_send_string(const char *str);
void uart_process_command(void);
```

#### 3.2.3 数据结构设计

**1. 系统配置数据结构**
```c
typedef struct {
    float input_ratio;          // 输入变比
    float input_threshold;      // 阈值设置
    uint8_t sampling_cycle;     // 采样周期
    uint8_t encrypt_mode;       // 加密模式
    uint32_t power_count;       // 开机计数
} system_config_t;
```

**2. 采样数据结构**
```c
typedef struct {
    uint32_t timestamp;         // 时间戳
    float voltage;              // 电压值
    uint32_t raw_adc;          // 原始ADC值
    uint8_t overlimit_flag;     // 超限标志
} sample_data_t;
```

**3. 系统状态数据结构**
```c
typedef struct {
    uint8_t sampling_active;    // 采样状态
    uint8_t storage_ready;      // 存储就绪
    uint8_t rtc_valid;         // RTC有效
    uint8_t error_code;        // 错误代码
    uint16_t data_count;       // 数据计数
} system_status_t;
```

### 3.3 数据流设计

#### 3.3.1 数据流架构

系统的数据流设计采用管道-过滤器模式，数据在各个处理阶段之间流动，每个阶段负责特定的数据处理功能。

**1. 数据采集流**
```
模拟信号 → ADC转换 → 数字滤波 → 单位转换 → 数据缓存
```

**2. 数据处理流**
```
原始数据 → 变比计算 → 阈值比较 → 格式转换 → 结果输出
```

**3. 数据存储流**
```
处理数据 → 分类判断 → 文件选择 → 格式化 → 写入存储
```

**4. 数据显示流**
```
实时数据 → 格式化 → 界面更新 → 用户显示
```

#### 3.3.2 数据流控制机制

**1. 数据缓冲机制**
- 使用环形缓冲区管理数据流
- 支持多生产者-多消费者模式
- 防止数据丢失和溢出

**2. 数据同步机制**
- 使用标志位进行数据同步
- 保证数据的一致性和完整性
- 避免数据竞争和冲突

**3. 数据验证机制**
- 对关键数据进行有效性检查
- 异常数据的检测和处理
- 数据完整性校验

#### 3.3.3 数据流优化策略

**1. 数据压缩**
- 对重复数据进行压缩存储
- 减少存储空间占用
- 提高数据传输效率

**2. 数据预处理**
- 在数据源头进行预处理
- 减少后续处理的计算负担
- 提高系统整体性能

**3. 数据缓存策略**
- 合理设置缓存大小
- 采用LRU等缓存替换算法
- 平衡内存使用和性能

### 3.4 控制流程设计

#### 3.4.1 系统启动流程

**1. 系统初始化序列**
```
系统上电 → 硬件初始化 → 外设配置 → 参数加载 → 自检测试 → 就绪状态
```

**详细启动流程：**
```c
int main(void)
{
    // 1. 基础硬件初始化
    bsp_led_init();
    bsp_btn_init();
    bsp_oled_init();
    bsp_gd25qxx_init();
    bsp_usart_init();
    bsp_adc_init();

    // 2. 文件系统初始化
    sd_fatfs_init();
    OLED_Init();

    // 3. 参数和配置初始化
    flash_stored();
    flash_ratio_threshold_init();
    power_on_count_init();

    // 4. 任务调度器初始化
    scheduler_init();
    all_timer_init();

    // 5. 系统状态输出
    my_printf(DEBUG_USART, "====system init====\n");
    store_log_entry("system init");

    // 6. 硬件自检
    if (sd_init_success) {
        store_log_entry("test ok");
    } else {
        store_log_entry("test error: tf card not found");
    }

    my_printf(DEBUG_USART, "\n====system ready====\n");

    // 7. 进入主循环
    while(1) {
        scheduler_run();
    }
}
```

#### 3.4.2 采样控制流程

**1. 采样启动流程**
```
启动命令 → 参数检查 → 定时器配置 → ADC使能 → 状态更新 → 开始采样
```

**2. 采样执行流程**
```
定时器中断 → 标志位设置 → ADC转换 → 数据处理 → 存储分发 → 状态更新
```

**3. 采样停止流程**
```
停止命令 → 定时器禁用 → ADC禁用 → 文件关闭 → 状态清理 → 系统复位
```

#### 3.4.3 异常处理流程

**1. 错误检测机制**
- 硬件故障检测：ADC、SD卡、Flash等
- 软件异常检测：内存溢出、栈溢出等
- 数据异常检测：超限、格式错误等

**2. 错误处理策略**
```
错误检测 → 错误分类 → 处理策略选择 → 错误处理 → 状态恢复 → 日志记录
```

**3. 系统恢复机制**
- 软件复位：重新初始化相关模块
- 硬件复位：系统重启恢复
- 数据恢复：从备份中恢复数据
- 状态恢复：恢复到安全状态

### 3.5 系统性能设计

#### 3.5.1 实时性设计

**1. 任务优先级设计**
- 高优先级：ADC数据处理（1ms周期）
- 中优先级：按键和串口处理（5ms周期）
- 低优先级：显示和日志处理（50ms周期）

**2. 中断响应设计**
- 中断嵌套控制：关键中断优先响应
- 中断处理时间：控制在微秒级别
- 中断屏蔽策略：保护关键代码段

**3. 任务调度优化**
- 时间片轮转调度：保证任务公平执行
- 优先级抢占：紧急任务优先处理
- 负载均衡：合理分配CPU资源

#### 3.5.2 资源利用设计

**1. 内存管理**
- 静态内存分配：避免内存碎片
- 栈空间优化：合理设置栈大小
- 缓冲区管理：循环使用缓冲区

**2. 存储管理**
- Flash磨损均衡：延长Flash使用寿命
- SD卡管理：合理分配存储空间
- 文件系统优化：提高读写效率

**3. 功耗管理**
- 低功耗模式：空闲时进入低功耗
- 外设管理：不用时关闭外设
- 时钟管理：动态调整时钟频率

---

## 第四章 工程系统优化

### 4.1 系统性能优化方案

#### 4.1.1 计算性能优化

**1. 算法优化策略**

**ADC数据处理优化：**
- 采用定点运算替代部分浮点运算，提高计算速度
- 使用查表法进行复杂数学运算，减少计算时间
- 优化电压转换算法，减少除法运算次数

```c
// 优化前的电压转换
float voltage = (adc_val * 3.3f * input_radio) / 4095.0f;

// 优化后的电压转换（使用预计算系数）
static const float adc_coefficient = 3.3f / 4095.0f;
float voltage = adc_val * adc_coefficient * input_radio;
```

**数据格式转换优化：**
- 使用位操作进行BCD码转换，提高转换效率
- 采用内联函数减少函数调用开销
- 优化字符串处理函数，减少内存拷贝

**2. 编译器优化配置**
- 启用O2级别编译优化，平衡代码大小和执行速度
- 使用内联汇编优化关键代码段
- 合理使用编译器指令，提示编译器优化方向

**3. 代码结构优化**
- 将频繁调用的函数放在RAM中执行
- 优化循环结构，减少循环开销
- 使用const关键字优化常量存储

#### 4.1.2 实时性能优化

**1. 中断响应优化**

**中断优先级配置：**
```c
// 中断优先级配置示例
void interrupt_priority_config(void)
{
    // ADC中断：最高优先级
    nvic_irq_enable(ADC_IRQn, 0, 0);

    // 定时器中断：高优先级
    nvic_irq_enable(TIMER6_IRQn, 1, 0);

    // 串口中断：中等优先级
    nvic_irq_enable(USART0_IRQn, 2, 0);

    // SD卡中断：低优先级
    nvic_irq_enable(SDIO_IRQn, 3, 0);
}
```

**中断处理优化：**
- 中断服务程序保持简短，复杂处理放在主循环
- 使用标志位进行中断与主程序的通信
- 避免在中断中进行浮点运算和复杂计算

**2. 任务调度优化**

**调度算法改进：**
```c
// 优化的任务调度器
void scheduler_run_optimized(void)
{
    static uint32_t last_check_time = 0;
    uint32_t current_time = get_system_ms();

    // 只在时间发生变化时检查任务
    if (current_time != last_check_time) {
        last_check_time = current_time;

        for (uint8_t i = 0; i < task_num; i++) {
            if (current_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run) {
                scheduler_task[i].last_run = current_time;
                scheduler_task[i].task_func();

                // 高优先级任务执行后立即检查其他任务
                if (scheduler_task[i].rate_ms <= 5) {
                    break;
                }
            }
        }
    }
}
```

**3. 内存访问优化**
- 数据结构对齐，提高内存访问效率
- 使用DMA进行大数据量传输
- 优化缓存使用，减少内存访问冲突

#### 4.1.3 存储性能优化

**1. Flash存储优化**

**磨损均衡策略：**
- 实现简单的磨损均衡算法，延长Flash使用寿命
- 使用多个扇区轮换存储配置数据
- 定期检查扇区使用情况，避免过度磨损

**写入优化策略：**
```c
// Flash写入优化
typedef struct {
    uint32_t sector_addr;
    uint32_t write_count;
    uint8_t is_valid;
} flash_sector_info_t;

static flash_sector_info_t sector_table[FLASH_SECTOR_COUNT];

uint32_t get_optimal_sector(void)
{
    uint32_t min_count = 0xFFFFFFFF;
    uint32_t optimal_sector = 0;

    for (uint8_t i = 0; i < FLASH_SECTOR_COUNT; i++) {
        if (sector_table[i].write_count < min_count) {
            min_count = sector_table[i].write_count;
            optimal_sector = sector_table[i].sector_addr;
        }
    }

    return optimal_sector;
}
```

**2. SD卡存储优化**

**文件系统优化：**
- 使用簇对齐写入，提高写入效率
- 批量写入数据，减少文件系统开销
- 定期同步文件系统，保证数据完整性

**缓存策略优化：**
```c
// SD卡写入缓存优化
#define WRITE_BUFFER_SIZE 512
static uint8_t write_buffer[WRITE_BUFFER_SIZE];
static uint16_t buffer_index = 0;

void optimized_file_write(const char *data, uint16_t length)
{
    if (buffer_index + length >= WRITE_BUFFER_SIZE) {
        // 缓存满时，批量写入
        f_write(&file_handle, write_buffer, buffer_index, &bytes_written);
        f_sync(&file_handle);
        buffer_index = 0;
    }

    // 将数据添加到缓存
    memcpy(&write_buffer[buffer_index], data, length);
    buffer_index += length;
}
```

### 4.2 资源利用优化

#### 4.2.1 内存资源优化

**1. RAM使用优化**

**内存分配策略：**
- 使用静态内存分配，避免内存碎片
- 合理规划全局变量和局部变量的使用
- 使用内存池技术管理动态内存需求

**栈空间优化：**
```c
// 栈使用优化示例
void stack_optimized_function(void)
{
    // 使用static变量减少栈使用
    static char temp_buffer[128];

    // 避免大数组在栈上分配
    // char large_array[1024];  // 不推荐

    // 使用指针传递大结构体
    process_large_data(&global_data_struct);
}
```

**缓冲区管理优化：**
- 实现环形缓冲区，提高内存利用率
- 使用双缓冲技术，提高数据处理效率
- 动态调整缓冲区大小，适应不同工作负载

**2. Flash资源优化**

**代码空间优化：**
- 移除未使用的库函数和代码
- 使用函数指针减少代码重复
- 合理使用内联函数，平衡代码大小和执行速度

**常量数据优化：**
```c
// 常量数据存储优化
const float voltage_lookup_table[256] PROGMEM = {
    // 预计算的电压转换表
    0.000f, 0.001f, 0.002f, ...
};

// 使用查表法替代计算
float get_voltage_from_adc(uint16_t adc_value)
{
    uint8_t index = adc_value >> 4;  // 简化索引计算
    return voltage_lookup_table[index];
}
```

#### 4.2.2 外设资源优化

**1. 定时器资源优化**

**定时器复用策略：**
- 使用一个定时器产生多个时间基准
- 通过软件分频实现不同周期的任务
- 合理配置定时器预分频，减少中断频率

**2. 通信接口优化**

**串口通信优化：**
```c
// 串口DMA传输优化
void uart_dma_config(void)
{
    // 配置DMA用于串口发送
    dma_parameter_struct dma_init_struct;

    dma_init_struct.direction = DMA_MEMORY_TO_PERIPHERAL;
    dma_init_struct.memory_inc = DMA_MEMORY_INCREASE_ENABLE;
    dma_init_struct.periph_inc = DMA_PERIPH_INCREASE_DISABLE;
    dma_init_struct.priority = DMA_PRIORITY_HIGH;

    dma_init(DMA0, DMA_CH3, &dma_init_struct);
}

void uart_send_dma(const char *data, uint16_t length)
{
    dma_memory_address_config(DMA0, DMA_CH3, (uint32_t)data);
    dma_transfer_number_config(DMA0, DMA_CH3, length);
    dma_channel_enable(DMA0, DMA_CH3);
}
```

**SPI接口优化：**
- 使用DMA进行SD卡数据传输
- 优化SPI时钟频率，平衡速度和稳定性
- 实现SPI命令队列，提高传输效率

### 4.3 可靠性优化

#### 4.3.1 错误检测与处理

**1. 硬件故障检测**

**ADC故障检测：**
```c
// ADC健康检查
uint8_t adc_health_check(void)
{
    uint32_t test_values[10];
    uint32_t sum = 0;

    // 连续采样10次
    for (uint8_t i = 0; i < 10; i++) {
        test_values[i] = adc_get_raw_value();
        sum += test_values[i];
        delay_ms(1);
    }

    uint32_t average = sum / 10;

    // 检查数据一致性
    for (uint8_t i = 0; i < 10; i++) {
        if (abs(test_values[i] - average) > ADC_TOLERANCE) {
            return ADC_ERROR_UNSTABLE;
        }
    }

    // 检查数据范围
    if (average < ADC_MIN_VALUE || average > ADC_MAX_VALUE) {
        return ADC_ERROR_RANGE;
    }

    return ADC_OK;
}
```

**存储设备检测：**
```c
// SD卡健康检查
uint8_t sd_card_health_check(void)
{
    FRESULT result;
    FIL test_file;
    UINT bytes_written, bytes_read;
    char test_data[] = "SD_CARD_TEST";
    char read_buffer[16];

    // 尝试创建测试文件
    result = f_open(&test_file, "0:/test.tmp", FA_CREATE_ALWAYS | FA_WRITE);
    if (result != FR_OK) {
        return SD_ERROR_WRITE;
    }

    // 写入测试数据
    result = f_write(&test_file, test_data, strlen(test_data), &bytes_written);
    f_close(&test_file);

    if (result != FR_OK || bytes_written != strlen(test_data)) {
        return SD_ERROR_WRITE;
    }

    // 读取并验证数据
    result = f_open(&test_file, "0:/test.tmp", FA_READ);
    if (result == FR_OK) {
        f_read(&test_file, read_buffer, strlen(test_data), &bytes_read);
        f_close(&test_file);
        f_unlink("0:/test.tmp");  // 删除测试文件

        if (memcmp(test_data, read_buffer, strlen(test_data)) == 0) {
            return SD_OK;
        }
    }

    return SD_ERROR_READ;
}
```

**2. 软件异常处理**

**看门狗机制：**
```c
// 看门狗配置和喂狗
void watchdog_init(void)
{
    fwdgt_config(FWDGT_PRESCALER_DIV256, 0x0FFF);  // 约4秒超时
    fwdgt_enable();
}

void watchdog_feed(void)
{
    fwdgt_counter_reload();
}

// 在主循环中定期喂狗
void main_loop(void)
{
    while (1) {
        scheduler_run();

        // 系统正常运行时喂狗
        if (system_status_check() == SYSTEM_OK) {
            watchdog_feed();
        }
    }
}
```

**异常恢复机制：**
```c
// 系统异常恢复
void system_error_recovery(uint8_t error_code)
{
    switch (error_code) {
        case ERROR_ADC_FAULT:
            // ADC故障恢复
            adc_deinit();
            delay_ms(100);
            adc_init();
            store_log_entry("ADC fault recovered");
            break;

        case ERROR_SD_FAULT:
            // SD卡故障恢复
            sd_fatfs_init();
            store_log_entry("SD card fault recovered");
            break;

        case ERROR_MEMORY_FAULT:
            // 内存故障恢复
            system_soft_reset();
            break;

        default:
            // 未知错误，系统重启
            system_hard_reset();
            break;
    }
}
```

#### 4.3.2 数据完整性保护

**1. 数据校验机制**

**CRC校验：**
```c
// CRC校验实现
uint16_t calculate_crc16(const uint8_t *data, uint16_t length)
{
    uint16_t crc = 0xFFFF;

    for (uint16_t i = 0; i < length; i++) {
        crc ^= data[i];
        for (uint8_t j = 0; j < 8; j++) {
            if (crc & 0x0001) {
                crc = (crc >> 1) ^ 0xA001;
            } else {
                crc = crc >> 1;
            }
        }
    }

    return crc;
}

// 数据存储时添加校验
void store_data_with_checksum(const char *filename, const void *data, uint16_t length)
{
    uint16_t checksum = calculate_crc16((const uint8_t *)data, length);

    f_write(&file, data, length, &bytes_written);
    f_write(&file, &checksum, sizeof(checksum), &bytes_written);
    f_sync(&file);
}
```

**2. 备份与恢复机制**

**配置数据备份：**
```c
// 配置数据备份策略
typedef struct {
    system_config_t config;
    uint16_t checksum;
    uint32_t version;
} config_backup_t;

void backup_system_config(void)
{
    config_backup_t backup;

    // 准备备份数据
    backup.config = current_config;
    backup.version = CONFIG_VERSION;
    backup.checksum = calculate_crc16((uint8_t *)&backup.config, sizeof(system_config_t));

    // 写入主备份区
    flash_write_data(CONFIG_PRIMARY_ADDR, (uint8_t *)&backup, sizeof(backup));

    // 写入备份区
    flash_write_data(CONFIG_BACKUP_ADDR, (uint8_t *)&backup, sizeof(backup));
}

uint8_t restore_system_config(void)
{
    config_backup_t primary, backup;

    // 读取主配置
    flash_read_data(CONFIG_PRIMARY_ADDR, (uint8_t *)&primary, sizeof(primary));

    // 验证主配置
    uint16_t calc_checksum = calculate_crc16((uint8_t *)&primary.config, sizeof(system_config_t));

    if (calc_checksum == primary.checksum && primary.version == CONFIG_VERSION) {
        current_config = primary.config;
        return CONFIG_RESTORE_PRIMARY;
    }

    // 主配置损坏，尝试备份配置
    flash_read_data(CONFIG_BACKUP_ADDR, (uint8_t *)&backup, sizeof(backup));
    calc_checksum = calculate_crc16((uint8_t *)&backup.config, sizeof(system_config_t));

    if (calc_checksum == backup.checksum && backup.version == CONFIG_VERSION) {
        current_config = backup.config;
        // 恢复主配置
        flash_write_data(CONFIG_PRIMARY_ADDR, (uint8_t *)&backup, sizeof(backup));
        return CONFIG_RESTORE_BACKUP;
    }

    // 所有配置都损坏，使用默认配置
    load_default_config();
    backup_system_config();
    return CONFIG_RESTORE_DEFAULT;
}
```

### 4.4 用户体验优化

#### 4.4.1 交互响应优化

**1. 按键响应优化**

**按键防抖优化：**
```c
// 改进的按键防抖算法
typedef struct {
    uint8_t state;
    uint8_t count;
    uint8_t last_state;
} key_debounce_t;

static key_debounce_t key_states[KEY_COUNT];

uint8_t debounce_key(uint8_t key_id, uint8_t current_state)
{
    key_debounce_t *key = &key_states[key_id];

    if (current_state != key->last_state) {
        key->count = 0;
        key->last_state = current_state;
    } else if (key->count < DEBOUNCE_COUNT) {
        key->count++;
    } else if (key->state != current_state) {
        key->state = current_state;
        return 1;  // 状态发生变化
    }

    return 0;  // 状态未变化
}
```

**2. 显示响应优化**

**OLED显示优化：**
```c
// 显示缓存优化
static char display_buffer[2][32];  // 双行显示缓存
static uint8_t display_changed[2] = {1, 1};

void oled_printf_optimized(uint8_t line, uint8_t col, const char *format, ...)
{
    char temp_buffer[32];
    va_list args;

    va_start(args, format);
    vsnprintf(temp_buffer, sizeof(temp_buffer), format, args);
    va_end(args);

    // 只有内容改变时才更新显示
    if (strcmp(display_buffer[line], temp_buffer) != 0) {
        strcpy(display_buffer[line], temp_buffer);
        display_changed[line] = 1;
    }
}

void oled_update_display(void)
{
    for (uint8_t line = 0; line < 2; line++) {
        if (display_changed[line]) {
            OLED_ShowString(0, line * 16, display_buffer[line], 16);
            display_changed[line] = 0;
        }
    }
}
```

#### 4.4.2 信息提示优化

**1. 状态指示优化**

**LED状态管理：**
```c
// LED状态管理器
typedef enum {
    LED_STATE_OFF,
    LED_STATE_ON,
    LED_STATE_BLINK_SLOW,
    LED_STATE_BLINK_FAST,
    LED_STATE_PULSE
} led_state_t;

typedef struct {
    led_state_t state;
    uint32_t last_update;
    uint8_t blink_count;
} led_control_t;

static led_control_t led_controls[LED_COUNT];

void led_set_state(uint8_t led_id, led_state_t state)
{
    led_controls[led_id].state = state;
    led_controls[led_id].last_update = get_system_ms();
    led_controls[led_id].blink_count = 0;
}

void led_update_all(void)
{
    uint32_t current_time = get_system_ms();

    for (uint8_t i = 0; i < LED_COUNT; i++) {
        led_control_t *led = &led_controls[i];

        switch (led->state) {
            case LED_STATE_BLINK_SLOW:
                if (current_time - led->last_update >= 500) {
                    led_toggle(i);
                    led->last_update = current_time;
                }
                break;

            case LED_STATE_BLINK_FAST:
                if (current_time - led->last_update >= 100) {
                    led_toggle(i);
                    led->last_update = current_time;
                }
                break;

            // 其他状态处理...
        }
    }
}
```

**2. 错误提示优化**

**分级错误提示：**
```c
// 错误提示分级
typedef enum {
    ERROR_LEVEL_INFO,     // 信息提示
    ERROR_LEVEL_WARNING,  // 警告
    ERROR_LEVEL_ERROR,    // 错误
    ERROR_LEVEL_CRITICAL  // 严重错误
} error_level_t;

void show_error_message(error_level_t level, const char *message)
{
    switch (level) {
        case ERROR_LEVEL_INFO:
            led_set_state(LED_STATUS, LED_STATE_BLINK_SLOW);
            oled_printf_optimized(1, 0, "Info: %s", message);
            break;

        case ERROR_LEVEL_WARNING:
            led_set_state(LED_WARNING, LED_STATE_BLINK_FAST);
            oled_printf_optimized(1, 0, "Warn: %s", message);
            my_printf(DEBUG_USART, "WARNING: %s\r\n", message);
            break;

        case ERROR_LEVEL_ERROR:
            led_set_state(LED_ERROR, LED_STATE_ON);
            oled_printf_optimized(1, 0, "Err: %s", message);
            my_printf(DEBUG_USART, "ERROR: %s\r\n", message);
            store_log_entry(message);
            break;

        case ERROR_LEVEL_CRITICAL:
            // 严重错误：所有LED闪烁
            for (uint8_t i = 0; i < LED_COUNT; i++) {
                led_set_state(i, LED_STATE_BLINK_FAST);
            }
            oled_printf_optimized(0, 0, "CRITICAL ERROR");
            oled_printf_optimized(1, 0, "%s", message);
            my_printf(DEBUG_USART, "CRITICAL: %s\r\n", message);
            store_log_entry(message);
            break;
    }
}
```

---
